<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Agent Chat</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f4f4f4; margin: 0; padding: 0; }
        .container { max-width: 500px; margin: 40px auto; background: #fff; padding: 24px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        h2 { text-align: center; }
        #response { margin-top: 20px; padding: 12px; background: #e9ecef; border-radius: 4px; min-height: 40px; }
        input, button { padding: 10px; font-size: 1rem; }
        input { width: 80%; border: 1px solid #ccc; border-radius: 4px; }
        button { width: 18%; background: #007bff; color: #fff; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        form { display: flex; gap: 2%; }
    </style>
</head>
<body>
    <div class="container">
        <h2>AI Agent Chat</h2>
        <form id="chat-form">
            <input type="text" id="message" placeholder="Type your message..." required />
            <button type="submit">Send</button>
        </form>
        <div id="response"></div>
    </div>
    <script>
        const form = document.getElementById('chat-form');
        const messageInput = document.getElementById('message');
        const responseDiv = document.getElementById('response');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const message = messageInput.value.trim();
            if (!message) return;
            responseDiv.textContent = 'Thinking...';
            try {
                const res = await fetch('/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message })
                });
                const data = await res.json();
                if (data.response) {
                    responseDiv.textContent = data.response;
                } else if (data.error) {
                    responseDiv.textContent = 'Error: ' + data.error;
                } else {
                    responseDiv.textContent = 'No response from AI.';
                }
            } catch (err) {
                responseDiv.textContent = 'Request failed.';
            }
            messageInput.value = '';
        });
    </script>
</body>
</html> 