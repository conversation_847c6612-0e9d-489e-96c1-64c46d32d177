from fastapi import FastAPI
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
import requests
import os
from dotenv import load_dotenv
import logging

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def validate_environment():
    """Validate that all required environment variables are set"""
    required_vars = ["OPENROUTER_API_KEY"]
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var) or os.getenv(var) == "your_openrouter_api_key_here":
            missing_vars.append(var)

    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please check your .env file and set the required variables.")
        return False

    logger.info("Environment validation passed ✓")
    return True

def check_dependencies():
    """Check if all required packages are installed"""
    required_packages = ["fastapi", "requests", "uvicorn", "python-dotenv"]
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        logger.error(f"Missing required packages: {', '.join(missing_packages)}")
        logger.error("Please run: pip install -r requirements.txt")
        return False

    logger.info("Dependencies check passed ✓")
    return True

app = FastAPI(title="AI Chat API", description="Chat with AI using OpenRouter API")

class ChatRequest(BaseModel):
    message: str

@app.on_event("startup")
async def startup_event():
    """Validate environment and dependencies on startup"""
    logger.info("Starting AI Chat Application...")

    if not check_dependencies():
        logger.error("Dependency check failed. Exiting...")
        return

    if not validate_environment():
        logger.error("Environment validation failed. Please check your .env file.")
        return

    logger.info("Application started successfully! 🚀")
    logger.info("Access the chat interface at: http://localhost:8000/")

@app.get("/")
async def root():
    """Serve the frontend HTML file"""
    return FileResponse("frontend.html")

@app.post("/chat")
async def chat_endpoint(chat_request: ChatRequest):
    """Handle chat requests and communicate with OpenRouter API"""
    logger.info(f"Received chat request: {chat_request.message[:50]}...")

    # Validate API key
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key or api_key == "your_openrouter_api_key_here":
        logger.error("OpenRouter API key not configured")
        return JSONResponse(
            status_code=500,
            content={"error": "OpenRouter API key not configured. Please check your .env file."}
        )

    # Validate message
    if not chat_request.message.strip():
        return JSONResponse(
            status_code=400,
            content={"error": "Message cannot be empty"}
        )

    # Prepare headers
    site_url = os.getenv("YOUR_SITE_URL", "")
    site_name = os.getenv("YOUR_SITE_NAME", "")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }

    if site_url:
        headers["HTTP-Referer"] = site_url
    if site_name:
        headers["X-Title"] = site_name

    # Prepare request data
    data = {
        "model": "meta-llama/llama-3.1-405b-instruct:free",
        "messages": [
            {"role": "user", "content": chat_request.message}
        ],
        "max_tokens": 1000,
        "temperature": 0.7
    }

    try:
        logger.info("Sending request to OpenRouter API...")
        response = requests.post(
            url="https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )

        # Handle different HTTP status codes
        if response.status_code == 401:
            logger.error("Invalid API key")
            return JSONResponse(
                status_code=500,
                content={"error": "Invalid API key. Please check your OpenRouter API key."}
            )
        elif response.status_code == 429:
            logger.error("Rate limit exceeded")
            return JSONResponse(
                status_code=500,
                content={"error": "Rate limit exceeded. Please try again later."}
            )
        elif response.status_code == 402:
            logger.error("Insufficient credits")
            return JSONResponse(
                status_code=500,
                content={"error": "Insufficient credits. Please check your OpenRouter account."}
            )

        response.raise_for_status()
        result = response.json()

        # Extract the AI's reply from the response structure
        if "choices" in result and len(result["choices"]) > 0:
            ai_message = result["choices"][0].get("message", {}).get("content", "No response")
            logger.info("Successfully received AI response")
            return {"response": ai_message}
        else:
            logger.error("Unexpected response structure from OpenRouter")
            return JSONResponse(
                status_code=500,
                content={"error": "Unexpected response from AI service"}
            )

    except requests.exceptions.Timeout:
        logger.error("Request timeout")
        return JSONResponse(
            status_code=500,
            content={"error": "Request timeout. Please try again."}
        )
    except requests.exceptions.ConnectionError:
        logger.error("Connection error")
        return JSONResponse(
            status_code=500,
            content={"error": "Connection error. Please check your internet connection."}
        )
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Request failed: {str(e)}"}
        )
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Unexpected error: {str(e)}"}
        )