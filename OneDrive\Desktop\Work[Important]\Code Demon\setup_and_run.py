#!/usr/bin/env python3
"""
AI Chat Application Setup and Launch Script
This script will:
1. Check Python version
2. Install dependencies
3. Validate environment configuration
4. Start the application
"""

import os
import sys
import subprocess
import platform
import webbrowser
import time
from pathlib import Path

def print_banner():
    """Print application banner"""
    print("=" * 60)
    print("🤖 AI Chat Application Setup & Launch")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required. Current version:", f"{version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def check_env_file():
    """Check if .env file exists and is configured"""
    print("\n🔧 Checking environment configuration...")
    
    env_path = Path(".env")
    if not env_path.exists():
        print("❌ .env file not found")
        return False
    
    # Read .env file and check for API key
    with open(env_path, 'r') as f:
        content = f.read()
    
    if "your_openrouter_api_key_here" in content:
        print("❌ OpenRouter API key not configured in .env file")
        print("📝 Please edit .env file and add your OpenRouter API key")
        print("   Get your API key from: https://openrouter.ai/")
        return False
    
    if "OPENROUTER_API_KEY=" not in content:
        print("❌ OPENROUTER_API_KEY not found in .env file")
        return False
    
    print("✅ Environment configuration looks good")
    return True

def start_server():
    """Start the FastAPI server"""
    print("\n🚀 Starting the AI Chat server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("🌐 Chat interface at: http://localhost:8000/")
    print("\n⏳ Starting server (this may take a few seconds)...")
    
    try:
        # Start the server
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--reload", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ])
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Open browser
        print("🌐 Opening browser...")
        webbrowser.open("http://localhost:8000")
        
        print("\n" + "=" * 60)
        print("✅ AI Chat Application is running!")
        print("📍 URL: http://localhost:8000")
        print("🛑 Press Ctrl+C to stop the server")
        print("=" * 60)
        
        # Wait for the process
        process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        process.terminate()
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return False
    
    return True

def main():
    """Main setup and launch function"""
    print_banner()
    
    # Check Python version
    if not check_python_version():
        print("\n❌ Setup failed: Incompatible Python version")
        input("Press Enter to exit...")
        return False
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed: Could not install dependencies")
        input("Press Enter to exit...")
        return False
    
    # Check environment configuration
    if not check_env_file():
        print("\n❌ Setup failed: Environment not configured")
        print("\n📝 Next steps:")
        print("1. Edit the .env file")
        print("2. Add your OpenRouter API key")
        print("3. Run this script again")
        input("Press Enter to exit...")
        return False
    
    # Start the server
    print("\n🎉 Setup complete! Starting the application...")
    start_server()
    
    return True

if __name__ == "__main__":
    main()
