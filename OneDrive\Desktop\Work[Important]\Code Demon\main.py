from fastapi import FastAP<PERSON>, Request
from pydantic import BaseModel
import requests
import json
import os
from fastapi.responses import JSONResponse

app = FastAPI()

class ChatRequest(BaseModel):
    message: str

@app.post("/chat")
async def chat_endpoint(chat_request: ChatRequest, request: Request):
    api_key = os.getenv("OPENROUTER_API_KEY")
    site_url = os.getenv("YOUR_SITE_URL", "")
    site_name = os.getenv("YOUR_SITE_NAME", "")
    if not api_key:
        return JSONResponse(status_code=500, content={"error": "OPENROUTER_API_KEY not set in environment"})
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }
    if site_url:
        headers["HTTP-Referer"] = site_url
    if site_name:
        headers["X-Title"] = site_name
    data = {
        "model": "meta-llama/llama-3.1-405b-instruct:free",
        "messages": [
            {"role": "user", "content": chat_request.message}
        ]
    }
    try:
        response = requests.post(
            url="https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            data=json.dumps(data)
        )
        response.raise_for_status()
        result = response.json()
        # Extract the AI's reply from the response structure
        ai_message = result.get("choices", [{}])[0].get("message", {}).get("content", "No response")
        return {"response": ai_message}
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)}) 